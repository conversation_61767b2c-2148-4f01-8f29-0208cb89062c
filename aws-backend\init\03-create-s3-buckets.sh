#!/bin/bash

# GameFlex S3 Buckets Creation Script
# This script creates S3 buckets and uploads initial media files
# Runs automatically when LocalStack starts

set -e

echo "[S3] Creating S3 buckets for GameFlex..."

# S3 bucket names (matching the Lambda function configuration)
MEDIA_BUCKET="gameflex-media-development"
AVATARS_BUCKET="gameflex-avatars-development"
TEMP_BUCKET="gameflex-temp-development"
CLIPART_BUCKET="gameflex-clipart-development"

# Function to create an S3 bucket
create_bucket() {
    local bucket_name=$1
    
    echo "[INFO] Creating S3 bucket: $bucket_name"
    
    # Check if bucket exists
    if awslocal s3api head-bucket --bucket "$bucket_name" >/dev/null 2>&1; then
        echo "[INFO] Bucket $bucket_name already exists"
        return 0
    fi
    
    # Create bucket
    awslocal s3api create-bucket --bucket "$bucket_name"
    
    # Set bucket policy to allow public read access for media files
    awslocal s3api put-bucket-policy --bucket "$bucket_name" --policy '{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "PublicReadGetObject",
                "Effect": "Allow",
                "Principal": "*",
                "Action": "s3:GetObject",
                "Resource": "arn:aws:s3:::'$bucket_name'/*"
            }
        ]
    }'
    
    echo "[INFO] Bucket $bucket_name created successfully"
}

# Function to upload files to S3
upload_files() {
    local source_dir=$1
    local bucket_name=$2
    local s3_prefix=$3
    
    if [ ! -d "$source_dir" ]; then
        echo "[WARN] Source directory $source_dir does not exist, skipping upload"
        return 0
    fi
    
    echo "[INFO] Uploading files from $source_dir to s3://$bucket_name/$s3_prefix"
    
    # Upload all files recursively
    find "$source_dir" -type f | while read -r file; do
        # Get relative path from source directory
        relative_path=$(realpath --relative-to="$source_dir" "$file")
        s3_key="$s3_prefix/$relative_path"
        
        echo "[INFO] Uploading $file to s3://$bucket_name/$s3_key"
        
        # Determine content type based on file extension
        case "${file##*.}" in
            jpg|jpeg) content_type="image/jpeg" ;;
            png) content_type="image/png" ;;
            gif) content_type="image/gif" ;;
            webp) content_type="image/webp" ;;
            mp4) content_type="video/mp4" ;;
            mov) content_type="video/quicktime" ;;
            avi) content_type="video/x-msvideo" ;;
            webm) content_type="video/webm" ;;
            *) content_type="application/octet-stream" ;;
        esac
        
        awslocal s3api put-object \
            --bucket "$bucket_name" \
            --key "$s3_key" \
            --body "$file" \
            --content-type "$content_type"
    done
}

# Create all S3 buckets
echo "[INFO] Creating S3 buckets..."
create_bucket "$MEDIA_BUCKET"
create_bucket "$AVATARS_BUCKET"
create_bucket "$TEMP_BUCKET"
create_bucket "$CLIPART_BUCKET"

# Upload clipart images
echo "[INFO] Uploading clipart images..."
upload_files "/opt/assets/images/clipart" "$CLIPART_BUCKET" "clipart"

# Upload sample media files
echo "[INFO] Uploading sample media files..."
upload_files "/opt/assets/media" "$MEDIA_BUCKET" "samples"

echo "[S3] S3 buckets and media files setup completed successfully!"
