version: '3.8'

services:
  localstack:
    container_name: "${LOCALSTACK_DOCKER_NAME:-gameflex-localstack}"
    image: localstack/localstack-pro  # required for Pro
    ports:
      - "127.0.0.1:4566:4566"            # LocalStack Gateway
      - "127.0.0.1:4510-4559:4510-4559"  # external services port range
      - "127.0.0.1:443:443"              # LocalStack HTTPS Gateway (Pro)
      - "45660:4566"                     # Keep existing port mapping for compatibility
    env_file:
      - .env
    environment:
      # Activate LocalStack Pro: https://docs.localstack.cloud/getting-started/auth-token/
      - LOCALSTACK_AUTH_TOKEN=${LOCALSTACK_AUTH_TOKEN:?}  # required for Pro
      # LocalStack configuration: https://docs.localstack.cloud/references/configuration/
      - DEBUG=${DEBUG:-1}
      - PERSISTENCE=${PERSISTENCE:-1}
      - LAMBDA_EXECUTOR=docker
      - DOCKER_HOST=unix:///var/run/docker.sock
      - HOST_TMP_FOLDER=${TMPDIR:-/tmp}/localstack

      # AWS Configuration
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test

      # Enable AWS services (LocalStack Pro compatible)
      - SERVICES=cognito-idp,cognito-identity,dynamodb,lambda,apigateway,s3,cloudformation,iam,sts,logs,secretsmanager,ssm

      # S3 configuration
      - S3_SKIP_SIGNATURE_VALIDATION=1

      # Lambda configuration
      - LAMBDA_RUNTIME_ENVIRONMENT_TIMEOUT=60
      - LAMBDA_REMOVE_CONTAINERS=true

      # API Gateway configuration
      - GATEWAY_LISTEN=0.0.0.0:4566

    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./init:/etc/localstack/init/ready.d"
      - "./lambda-functions:/opt/lambda-functions"
      - "./cloudformation:/opt/cloudformation"
      - "./assets:/opt/assets"
    networks:
      - gameflex-aws
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

networks:
  gameflex-aws:
    driver: bridge
