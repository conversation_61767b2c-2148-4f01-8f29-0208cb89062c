import { Paginator } from "@smithy/types";
import {
  ListContributorInsightsCommandInput,
  ListContributorInsightsCommandOutput,
} from "../commands/ListContributorInsightsCommand";
import { DynamoDBPaginationConfiguration } from "./Interfaces";
export declare const paginateListContributorInsights: (
  config: DynamoDBPaginationConfiguration,
  input: ListContributorInsightsCommandInput,
  ...rest: any[]
) => Paginator<ListContributorInsightsCommandOutput>;
