#!/bin/bash

# GameFlex AWS Services Initialization Script (Shell)
# This script initializes AWS services in LocalStack
# Runs automatically when LocalStack starts

set -e

echo "[AWS-INIT] Initializing AWS services in LocalStack..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[AWS-INIT]${NC} $1"
}

# Test AWS CLI and LocalStack connectivity
test_aws_connection() {
    if awslocal sts get-caller-identity > /dev/null 2>&1; then
        print_status "AWS CLI connection to LocalStack successful"
        return 0
    else
        print_error "Failed to connect to LocalStack"
        return 1
    fi
}

# Create S3 buckets
create_s3_buckets() {
    print_status "Creating S3 buckets..."
    
    local buckets=(
        "gameflex-media-development"
        "gameflex-avatars-development"
        "gameflex-temp-development"
        "gameflex-clipart-development"
    )
    
    for bucket in "${buckets[@]}"; do
        if awslocal s3api head-bucket --bucket "$bucket" > /dev/null 2>&1; then
            print_status "Bucket $bucket already exists"
        else
            awslocal s3api create-bucket --bucket "$bucket"
            print_status "Created bucket: $bucket"
        fi
    done
}

# Test DynamoDB tables
test_dynamodb_tables() {
    print_status "Checking DynamoDB tables..."
    
    local tables=(
        "Users"
        "UserProfiles"
        "Channels"
        "ChannelMembers"
        "Posts"
        "Comments"
        "Likes"
        "Follows"
        "Notifications"
        "Media"
    )
    
    for table in "${tables[@]}"; do
        if awslocal dynamodb describe-table --table-name "$table" > /dev/null 2>&1; then
            print_status "Table $table exists"
        else
            print_warning "Table $table does not exist - will be created by CloudFormation"
        fi
    done
}

# Create Cognito User Pool
create_cognito_user_pool() {
    print_status "Creating Cognito User Pool..."
    
    # Check if user pool exists
    local existing_pool_id=$(awslocal cognito-idp list-user-pools --max-results 50 \
        --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' \
        --output text | head -n1)
    
    if [ -n "$existing_pool_id" ] && [ "$existing_pool_id" != "None" ]; then
        print_status "User pool already exists with ID: $existing_pool_id"
        USER_POOL_ID="$existing_pool_id"
    else
        local pool_result=$(awslocal cognito-idp create-user-pool \
            --pool-name "GameFlex-Development" \
            --policies '{
                "PasswordPolicy": {
                    "MinimumLength": 8,
                    "RequireUppercase": false,
                    "RequireLowercase": false,
                    "RequireNumbers": false,
                    "RequireSymbols": false
                }
            }' \
            --auto-verified-attributes email \
            --username-attributes email \
            --query 'UserPool.Id' \
            --output text)
        
        USER_POOL_ID="$pool_result"
        print_status "Created User Pool with ID: $USER_POOL_ID"
    fi
    
    # Check if client exists
    local existing_client_id=$(awslocal cognito-idp list-user-pool-clients \
        --user-pool-id "$USER_POOL_ID" \
        --query 'UserPoolClients[?ClientName==`GameFlex-Mobile-Client`].ClientId' \
        --output text | head -n1)
    
    if [ -n "$existing_client_id" ] && [ "$existing_client_id" != "None" ]; then
        print_status "User pool client already exists with ID: $existing_client_id"
        CLIENT_ID="$existing_client_id"
    else
        local client_result=$(awslocal cognito-idp create-user-pool-client \
            --user-pool-id "$USER_POOL_ID" \
            --client-name "GameFlex-Mobile-Client" \
            --generate-secret \
            --explicit-auth-flows ADMIN_NO_SRP_AUTH USER_PASSWORD_AUTH ALLOW_REFRESH_TOKEN_AUTH \
            --query 'UserPoolClient.ClientId' \
            --output text)
        
        CLIENT_ID="$client_result"
        print_status "Created User Pool Client with ID: $CLIENT_ID"
    fi
    
    # Update .env file with Cognito IDs
    update_env_file "$USER_POOL_ID" "$CLIENT_ID"
}

# Create Cognito Identity Pool
create_cognito_identity_pool() {
    print_status "Creating Cognito Identity Pool..."
    
    # Check if identity pool exists
    local existing_identity_pool_id=$(awslocal cognito-identity list-identity-pools --max-results 10 \
        --query "IdentityPools[?IdentityPoolName=='gameflex-identity'].IdentityPoolId" \
        --output text | head -n1)
    
    if [ -n "$existing_identity_pool_id" ] && [ "$existing_identity_pool_id" != "None" ]; then
        print_status "Identity pool already exists with ID: $existing_identity_pool_id"
        IDENTITY_POOL_ID="$existing_identity_pool_id"
    else
        # Create temporary JSON file for identity pool configuration
        cat > temp-identity-pool.json << EOF
{
    "IdentityPoolName": "gameflex-identity",
    "AllowUnauthenticatedIdentities": false,
    "CognitoIdentityProviders": [
        {
            "ProviderName": "cognito-idp.us-east-1.amazonaws.com/$USER_POOL_ID",
            "ClientId": "$CLIENT_ID",
            "ServerSideTokenCheck": false
        }
    ]
}
EOF
        
        local identity_result=$(awslocal cognito-identity create-identity-pool \
            --cli-input-json file://temp-identity-pool.json \
            --query 'IdentityPoolId' \
            --output text)
        
        rm -f temp-identity-pool.json
        IDENTITY_POOL_ID="$identity_result"
        print_status "Created Cognito Identity Pool with ID: $IDENTITY_POOL_ID"
    fi
}

# Update .env file with generated IDs
update_env_file() {
    local user_pool_id=$1
    local client_id=$2
    
    if [ -f ".env" ]; then
        # Create a temporary file with updated content
        awk -v pool_id="$user_pool_id" -v client_id="$client_id" '
        /^COGNITO_USER_POOL_ID=/ { print "COGNITO_USER_POOL_ID=" pool_id; next }
        /^COGNITO_USER_POOL_CLIENT_ID=/ { print "COGNITO_USER_POOL_CLIENT_ID=" client_id; next }
        { print }
        ' .env > .env.tmp && mv .env.tmp .env
        
        print_status "Updated .env file with Cognito configuration"
    else
        print_warning ".env file not found, skipping update"
    fi
}

# Main execution
main() {
    print_header "Initializing AWS services in LocalStack..."
    echo
    
    if ! test_aws_connection; then
        print_error "Cannot connect to LocalStack. Make sure it's running."
        exit 1
    fi
    
    create_s3_buckets
    test_dynamodb_tables
    create_cognito_user_pool
    create_cognito_identity_pool
    
    echo
    print_status "AWS Services initialized successfully!"
    echo
    print_status "Cognito Configuration:"
    echo -e "  User Pool ID: ${CYAN}$USER_POOL_ID${NC}"
    echo -e "  Client ID: ${CYAN}$CLIENT_ID${NC}"
    echo -e "  Identity Pool ID: ${CYAN}$IDENTITY_POOL_ID${NC}"
    echo
    print_status "Initialization completed!"
}

# Run main function
main "$@"
